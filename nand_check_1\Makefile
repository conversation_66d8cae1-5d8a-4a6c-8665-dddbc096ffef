# CC=/usr/local/arm/cross/am335xt3/devkit/bin/arm-arago-linux-gnueabi-gcc
#3568
# CC=aarch64-linux-gnu-gcc
#d9
#CC=/tool/gcc_linaro/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin/aarch64-linux-gnu-gcc



check_nand:nanddump.o libmtd.o
	$(CC)	-Wall	nanddump.o libmtd.o -o  check_nand
nanddump.o:nanddump.c libmtd_int.h libmtd.h

	$(CC)	-c	-Wall	nanddump.c	-o	nanddump.o 
libmtd.o:libmtd.c libmtd.h
	$(CC)	-c	-Wall	libmtd.c	-o	libmtd.o

clean:
	$(RM) *.o	check_nand

