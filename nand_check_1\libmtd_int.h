/*
 * Copyright (c) International Business Machines Corp., 2006
 * Copyright (C) 2009 Nokia Corporation
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See
 * the GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
 *
 * Author: Artem Bityutskiy
 *
 * MTD library.
 */

#ifndef __LIBMTD_INT_H__
#define __LIBMTD_INT_H__

#include <errno.h>
#include <stdio.h>
#include <string.h>

#ifdef __cplusplus
extern "C"
{
#endif

#define ARRAY_SIZE(a) (sizeof(a) / sizeof((a)[0]))
/* Normal messages */
#define normsg_cont(fmt, ...) do {                                 \
	printf("%s: " fmt, PROGRAM_NAME, ##__VA_ARGS__);           \
} while(0)
#define normsg(fmt, ...) do {                                      \
	normsg_cont(fmt "\n", ##__VA_ARGS__);                      \
} while(0)

#ifdef __USE_FILE_OFFSET64
#define PRIxoff_t PRIx64
#define PRIdoff_t PRId64
#else
#define PRIxoff_t "l"PRIx32
#define PRIdoff_t "l"PRId32
#endif

#define PROGRAM_NAME "libmtd"

#define SYSFS_MTD "class/mtd"
#define MTD_NAME_PATT "mtd%d"
#define MTD_DEV "dev"
#define MTD_NAME "name"
#define MTD_TYPE "type"
#define MTD_EB_SIZE "erasesize"
#define MTD_SIZE "size"
#define MTD_MIN_IO_SIZE "writesize"
#define MTD_SUBPAGE_SIZE "subpagesize"
#define MTD_OOB_SIZE "oobsize"
#define MTD_REGION_CNT "numeraseregions"
#define MTD_FLAGS "flags"
#define MTD_PROC_FILE "/proc/mtd"
#define MTD_DEV_PATT  "/dev/mtd%d"

#define PROC_MTD_FIRST     "dev:    size   erasesize  name\n"
#define PROC_MTD_FIRST_LEN (sizeof(PROC_MTD_FIRST) - 1)
#define PROC_MTD_MAX_LEN   4096
#define PROC_MTD_PATT      "mtd%d: %llx %x"
#define MTD_DEV_MAJOR 90


#define OFFS64_IOCTLS_UNKNOWN 0
#define OFFS64_IOCTLS_NOT_SUPPORTED 1
#define OFFS64_IOCTLS_SUPPORTED 2
/* Error messages */
#define errmsg(fmt, ...)  ({                                                \
	fprintf(stderr, "%s: error!: " fmt "\n", "nand_check", ##__VA_ARGS__); \
	-1;                                                                 \
})
#define errmsg_die(fmt, ...) do {                                           \
	exit(errmsg(fmt, ##__VA_ARGS__));                                   \
} while(0)

/* System error messages */
#define sys_errmsg(fmt, ...)  ({                                            \
	int _err = errno;                                                   \
	errmsg(fmt, ##__VA_ARGS__);                                         \
	fprintf(stderr, "%*serror %d (%s)\n", (int)sizeof("nand_check") + 1,\
		"", _err, strerror(_err));                                  \
	-1;                                                                 \
})
#define sys_errmsg_die(fmt, ...) do {                                       \
	exit(sys_errmsg(fmt, ##__VA_ARGS__));                               \
} while(0)

	//__attribute__((unused))
	static void *xmalloc(size_t size)
	{
		void *ptr = malloc(size);

		// if (ptr == NULL && size != 0)
		// sys_errmsg_die("out of memory");
		return ptr;
	}
	__attribute__((unused))
static void *xcalloc(size_t nmemb, size_t size)
{
	void *ptr = calloc(nmemb, size);

	//if (ptr == NULL && nmemb != 0 && size != 0)
		//sys_errmsg_die("out of memory");
	return ptr;
}

__attribute__((unused))
static void *xzalloc(size_t size)
{
	return xcalloc(1, size);
}


	/**
	 * libmtd - MTD library description data structure.
	 * @sysfs_mtd: MTD directory in sysfs
	 * @mtd: MTD device sysfs directory pattern
	 * @mtd_dev: MTD device major/minor numbers file pattern
	 * @mtd_name: MTD device name file pattern
	 * @mtd_type: MTD device type file pattern
	 * @mtd_eb_size: MTD device eraseblock size file pattern
	 * @mtd_size: MTD device size file pattern
	 * @mtd_min_io_size: minimum I/O unit size file pattern
	 * @mtd_subpage_size: sub-page size file pattern
	 * @mtd_oob_size: MTD device OOB size file pattern
	 * @mtd_region_cnt: count of additional erase regions file pattern
	 * @mtd_flags: MTD device flags file pattern
	 * @sysfs_supported: non-zero if sysfs is supported by MTD
	 * @offs64_ioctls: %OFFS64_IOCTLS_SUPPORTED if 64-bit %MEMERASE64,
	 *                 %MEMREADOOB64, %MEMWRITEOOB64 MTD device ioctls are
	 *                 supported, %OFFS64_IOCTLS_NOT_SUPPORTED if not, and
	 *                 %OFFS64_IOCTLS_UNKNOWN if it is not known yet;
	 *
	 *  Note, we cannot find out whether 64-bit ioctls are supported by MTD when we
	 *  are initializing the library, because this requires an MTD device node.
	 *  Indeed, we have to actually call the ioctl and check for %ENOTTY to find
	 *  out whether it is supported or not.
	 *
	 *  Thus, we leave %offs64_ioctls uninitialized in 'libmtd_open()', and
	 *  initialize it later, when corresponding libmtd function is used, and when
	 *  we actually have a device node and can invoke an ioctl command on it.
	 */
	struct libmtd
	{
		char *sysfs_mtd;
		char *mtd;
		char *mtd_dev;
		char *mtd_name;
		char *mtd_type;
		char *mtd_eb_size;
		char *mtd_size;
		char *mtd_min_io_size;
		char *mtd_subpage_size;
		char *mtd_oob_size;
		char *mtd_region_cnt;
		char *mtd_flags;
		unsigned int sysfs_supported : 1;
		unsigned int offs64_ioctls : 2;
	};

	/**
 * struct proc_parse_info - /proc/mtd parsing information.
 * @mtd_num: MTD device number
 * @size: device size
 * @eb_size: eraseblock size
 * @name: device name
 * @buf: contents of /proc/mtd
 * @data_size: how much data was read into @buf
 * @pos: next string in @buf to parse
 */
struct proc_parse_info
{
	int mtd_num;
	long long size;
	char name[MTD_NAME_MAX + 1];
	int eb_size;
	char *buf;
	int data_size;
	char *next;
};

	int legacy_libmtd_open(void);
	int legacy_dev_present(int mtd_num);
	int legacy_mtd_get_info(struct mtd_info *info);
	int legacy_get_dev_info(const char *node, struct mtd_dev_info *mtd);
	int legacy_get_dev_info1(int dev_num, struct mtd_dev_info *mtd);

#ifdef __cplusplus
}
#endif

#endif /* !__LIBMTD_INT_H__ */
